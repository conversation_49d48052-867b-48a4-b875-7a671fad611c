import websockets
import threading
import asyncio
import logging
import json
import time
import hmac
import hashlib
import os


URI = "wss://contract.mexc.com/edge"
CONFIG_FILE = os.path.join(os.path.dirname(__file__), "../../../../trading-bot-config.json")

log = logging.getLogger(__name__)


def load_config():
    """Load API credentials from JSON configuration file with comments support"""
    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            content = f.read()

        in_string = False
        escape_next = False
        cleaned_chars = []

        i = 0
        while i < len(content):
            char = content[i]

            if escape_next:
                cleaned_chars.append(char)
                escape_next = False
                i += 1
                continue

            if char == "\\" and in_string:
                escape_next = True
                cleaned_chars.append(char)
                i += 1
                continue

            if char == '"' and not escape_next:
                in_string = not in_string
                cleaned_chars.append(char)
                i += 1
                continue

            if not in_string and char == "/" and i + 1 < len(content) and content[i + 1] == "/":
                while i < len(content) and content[i] != "\n":
                    i += 1
                continue

            cleaned_chars.append(char)
            i += 1

        cleaned_content = "".join(cleaned_chars)
        config = json.loads(cleaned_content)

        api_key = config.get("mexc-api-key", "")
        api_secret = config.get("mexc-api-secret", "")

        if not api_key or not api_secret:
            log.error("MEXC API credentials not found in config file")
            return None, None

        print(f"Loaded API key: {api_key[:10]}...")
        return api_key, api_secret

    except Exception as e:
        log.error(f"Failed to load config: {e}")
        return None, None


def generate_signature(api_key: str, api_secret: str, timestamp: str) -> str:
    """Generate signature for MEXC authentication"""
    try:
        signature_string = api_key + timestamp
        signature = hmac.new(api_secret.encode("utf-8"), signature_string.encode("utf-8"), hashlib.sha256).hexdigest()
        return signature
    except Exception as e:
        log.error(f"Failed to generate signature: {e}")
        return ""


def create_login_message(api_key: str, api_secret: str) -> dict:
    """Create login message for authentication"""
    timestamp = str(int(time.time() * 1000))
    signature = generate_signature(api_key, api_secret, timestamp)

    return {"method": "login", "param": {"apiKey": api_key, "reqTime": timestamp, "signature": signature}}


def methods(symbol: str):
    return {"method": "sub.deal", "param": {"symbol": symbol}}, {
        "method": "sub.funding.rate",
        "param": {"symbol": symbol},
    }


def handle_personal_order(data):
    """Handle push.personal.order messages"""
    print(f"=== PERSONAL ORDER UPDATE ===")
    print(f"Order ID: {data.get('orderId')}")
    print(f"Symbol: {data.get('symbol')}")
    print(f"Side: {data.get('side')} (1=open long, 2=close short, 3=open short, 4=close long)")
    print(f"Price: {data.get('price')}")
    print(f"Volume: {data.get('vol')}")
    print(f"Deal Volume: {data.get('dealVol')}")
    print(f"Deal Avg Price: {data.get('dealAvgPrice')}")
    print(f"State: {data.get('state')} (1=uninformed, 2=uncompleted, 3=completed, 4=cancelled, 5=invalid)")
    print(f"Error Code: {data.get('errorCode')}")
    print(f"External OID: {data.get('externalOid')}")
    print(f"Create Time: {data.get('createTime')}")
    print(f"Update Time: {data.get('updateTime')}")
    print("=" * 30)


def handle_message(data):
    """Handle different types of WebSocket messages"""
    channel = data.get("channel", "")

    if channel == "push.personal.order":
        handle_personal_order(data.get("data", {}))
    elif channel == "rs.login":
        print(f"Login result: {data.get('data')}")
    elif channel == "rs.error":
        print(f"Error: {data.get('data')}")
    elif channel == "pong":
        ... # print(f"Pong received: {data.get('data')}")
    elif channel == 'push.deal':
        ... # print(f"Received push.deal -> {data}")
    elif channel == 'push.funding.rate':
        ... # print(f'{data}')
    elif channel == 'push.personal.order.deal':
        ... # 
    else:
        print(f'channel = {channel} -> {data}')


async def connect():
    api_key, api_secret = load_config()
    if not api_key or not api_secret:
        log.error("Cannot connect without API credentials")
        return

    SYMBOLS = [
        "SOL_USDT",
    ]

    while True:
        try:
            async with websockets.connect(URI, ping_interval=None) as web:
                print("Connected to MEXC WebSocket")

                login_msg = create_login_message(api_key, api_secret)
                await web.send(json.dumps(login_msg))
                print("Login message sent")

                await asyncio.sleep(2)

                for symbol in SYMBOLS:
                    sub_deal, sub_fund = methods(symbol)
                    await web.send(json.dumps(sub_deal))
                    await web.send(json.dumps(sub_fund))
                    print(f"Subscribed to {symbol} deal and funding rate")

                start = 0

                while True:
                    try:
                        r = await web.recv()
                        data = json.loads(r)
                        handle_message(data)

                        if time.time() - start > 10:
                            loop = asyncio.get_event_loop()
                            loop.create_task(web.send(json.dumps({"method": "ping"})))
                            start = time.time()
                    except Exception as e:
                        log.error(f"Message handling error: {e}")
                        break
        except Exception as e:
            log.error(f"Connection error: {e}")
            await asyncio.sleep(5)


asyncio.run(connect())
