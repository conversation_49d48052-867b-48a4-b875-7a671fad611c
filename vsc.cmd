0. WebSocket position changes

1. WebSocket monitor plan Orders
  order state,1 uninformed,2 uncompleted,3 completed,4 cancelled,5 invalid

  channel = push.personal.plan.order -> {'channel': 'push.personal.plan.order', 'data': {'createTime': 1754683424287, 'executeCycle': 87600, 'id': '708892003813926400', 'leverage': 100, 'lossTrend': 1, 'openType': 2, 'orderType': 5, 'positionMode': 1, 'profitTrend': 1, 'reduceOnly': False, 'side': 1, 'state': 1(place), 'symbol': 'SUI_USDT', 'trend': 1, 'triggerPrice': 2, 'triggerType': 2, 'updateTime': 1754683424287, 'vol': 7}, 'ts': 1754683424295}
  
  channel = push.personal.plan.order -> {'channel': 'push.personal.plan.order', 'data': {'createTime': 1754683424287, 'executeCycle': 87600, 'id': '708892003813926400', 'leverage': 100, 'lossTrend': 1, 'openType': 2, 'orderType': 5, 'positionMode': 1, 'profitTrend': 1, 'reduceOnly': False, 'side': 1, 'state': 2(cancel), 'symbol': 'SUI_USDT', 'trend': 1, 'triggerPrice': 2, 'triggerType': 2, 'updateTime': 1754683453773, 'vol': 7}, 'ts': 1754683453778}


2. integrate orders.py logic to C#

